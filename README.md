# Spectrogram Analyzer

A Python script to generate spectrograms from audio files for visualizing tones and patterns vs. frequency over time.

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

```bash
python analyzer.py <input_file> <output_file> <output_type>
```

### Arguments

- `input_file`: Path to the input audio file (supports WAV, MP3, FLAC, etc.)
- `output_file`: Path for the output file (extension will be adjusted based on output type)
- `output_type`: Type of output to generate

### Output Types

- **`png`**: Standard PNG image (high quality, 300 DPI)
- **`svg`**: Scalable Vector Graphics (perfect for publications)
- **`pdf`**: PDF format (ideal for reports)
- **`interactive`**: Interactive HTML plot with zoom/pan capabilities (requires plotly)
- **`data`**: Raw spectrogram data as CSV file + metadata JSON

## Examples

```bash
# Generate a PNG spectrogram
python analyzer.py audio.wav spectrogram.png png

# Create an interactive HTML plot
python analyzer.py music.mp3 output/result.html interactive

# Export raw data for further analysis
python analyzer.py sound.flac data/analysis.csv data

# Generate a PDF for publication
python analyzer.py recording.wav figures/spectrogram.pdf pdf
```

## Features

- **High-quality spectrograms** using Short-Time Fourier Transform (STFT)
- **Multiple output formats** for different use cases
- **Interactive visualizations** with hover information
- **Raw data export** for custom analysis
- **Automatic file extension handling**
- **Comprehensive error handling**

## Technical Details

- Uses librosa for audio processing and STFT computation
- 2048-point FFT with 512-sample hop length
- Magnitude displayed in decibels (dB)
- Viridis colormap for optimal perceptual uniformity
- Supports all audio formats supported by librosa/soundfile

## Requirements

- Python 3.7+
- librosa (audio processing)
- matplotlib (plotting)
- numpy (numerical computations)
- scipy (signal processing)
- plotly (optional, for interactive plots)
- soundfile (audio file I/O)
