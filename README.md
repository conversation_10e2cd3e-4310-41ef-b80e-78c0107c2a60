# Spectrogram Analyzer & YouTube Audio Downloader

A comprehensive toolkit for audio analysis:
- **Spectrogram Analyzer**: Generate spectrograms from audio files for visualizing tones and patterns vs. frequency over time
- **YouTube Audio Downloader**: Download audio from YouTube videos and save as high-quality FLAC files

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Spectrogram Analyzer

```bash
python analyzer.py <input_file> <output_file> <output_type>
```

### YouTube Audio Downloader

```bash
python ytdl.py <youtube_url>
```

### Arguments

- `input_file`: Path to the input audio file (supports WAV, MP3, FLAC, etc.)
- `output_file`: Path for the output file (extension will be adjusted based on output type)
- `output_type`: Type of output to generate

### Output Types

- **`png`**: Standard PNG image (high quality, 300 DPI)
- **`svg`**: Scalable Vector Graphics (perfect for publications)
- **`pdf`**: PDF format (ideal for reports)
- **`interactive`**: Interactive HTML plot with zoom/pan capabilities (requires plotly)
- **`data`**: Raw spectrogram data as CSV file + metadata JSON

## Examples

### Spectrogram Analysis

```bash
# Generate a PNG spectrogram
python analyzer.py audio.wav spectrogram.png png

# Create an interactive HTML plot
python analyzer.py music.mp3 output/result.html interactive

# Export raw data for further analysis
python analyzer.py sound.flac data/analysis.csv data

# Generate a PDF for publication
python analyzer.py recording.wav figures/spectrogram.pdf pdf
```

### YouTube Audio Download

```bash
# Download audio from a YouTube video
python ytdl.py "https://www.youtube.com/watch?v=dQw4w9WgXcQ"

# Download to a specific directory
python ytdl.py "https://youtu.be/dQw4w9WgXcQ" -o downloads/

# Combined workflow: Download and analyze
python ytdl.py "https://www.youtube.com/watch?v=example"
python analyzer.py nevergonna.flac spectrogram.png png
```

## Features

### Spectrogram Analyzer
- **High-quality spectrograms** using Short-Time Fourier Transform (STFT)
- **Multiple output formats** for different use cases
- **Interactive visualizations** with hover information
- **Raw data export** for custom analysis
- **Automatic file extension handling**
- **Comprehensive error handling**

### YouTube Audio Downloader
- **High-quality FLAC audio** download from YouTube
- **Smart filename generation** from video title (first two words)
- **Automatic duplicate handling** with numbered suffixes
- **URL validation** for YouTube links
- **Progress feedback** with video information
- **Error handling** for network and conversion issues

## Technical Details

- Uses librosa for audio processing and STFT computation
- 2048-point FFT with 512-sample hop length
- Magnitude displayed in decibels (dB)
- Viridis colormap for optimal perceptual uniformity
- Supports all audio formats supported by librosa/soundfile

## Requirements

- Python 3.7+
- librosa (audio processing)
- matplotlib (plotting)
- numpy (numerical computations)
- scipy (signal processing)
- plotly (optional, for interactive plots)
- soundfile (audio file I/O)
- yt-dlp (YouTube downloading)
- ffmpeg (audio conversion - external dependency)
