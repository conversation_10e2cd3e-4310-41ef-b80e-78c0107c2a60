#!/usr/bin/env python3
"""
YouTube Audio Downloader

A Python script to download audio from YouTube videos and save as FLAC format.
The filename is generated from the first two words of the video title.

Usage:
    python ytdl.py <youtube_url>

Requirements:
    - yt-dlp (YouTube downloader)
    - ffmpeg (audio conversion)
"""

import argparse
import sys
import os
import re
import subprocess
from pathlib import Path
from urllib.parse import urlparse

try:
    import yt_dlp
except ImportError:
    print("Error: yt-dlp is required. Install with: pip install yt-dlp")
    sys.exit(1)


class YouTubeAudioDownloader:
    """Main class for downloading YouTube audio as FLAC."""

    def __init__(self, url: str, output_dir: str = "."):
        self.url = url
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Validate YouTube URL
        if not self._is_valid_youtube_url(url):
            raise ValueError(f"Invalid YouTube URL: {url}")

    def _is_valid_youtube_url(self, url: str) -> bool:
        """Validate if the URL is a valid YouTube URL."""
        youtube_domains = [
            'youtube.com', 'www.youtube.com', 'm.youtube.com',
            'youtu.be', 'www.youtu.be'
        ]

        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()

            # Check for youtube.com URLs
            if any(domain == yt_domain for yt_domain in youtube_domains if 'youtube.com' in yt_domain):
                return 'watch' in parsed.path or 'v=' in parsed.query

            # Check for youtu.be URLs
            if any(domain == yt_domain for yt_domain in youtube_domains if 'youtu.be' in yt_domain):
                return len(parsed.path) > 1

            return False
        except Exception:
            return False

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename by removing invalid characters."""
        # Remove invalid characters for filenames
        sanitized = re.sub(r'[<>:"/\\|?*]', '', filename)
        # Replace multiple spaces with single space
        sanitized = re.sub(r'\s+', ' ', sanitized)
        # Remove leading/trailing spaces
        sanitized = sanitized.strip()
        return sanitized

    def _get_video_info(self) -> dict:
        """Get video information using yt-dlp."""
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
        }

        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(self.url, download=False)
                return info
        except Exception as e:
            raise RuntimeError(f"Failed to get video information: {e}")

    def _generate_filename(self, title: str) -> str:
        """Generate filename from the first two words of the title."""
        import unicodedata

        # Clean and split title
        clean_title = self._sanitize_filename(title)
        words = clean_title.split()

        if len(words) == 0:
            base_name = "unknown"
        elif len(words) == 1:
            base_name = words[0]
        else:
            # Take first two words and join without space
            base_name = words[0] + words[1]

        # Normalize Unicode characters (remove accents, etc.)
        base_name = unicodedata.normalize('NFD', base_name)
        base_name = ''.join(c for c in base_name if unicodedata.category(c) != 'Mn')

        # Remove any remaining special characters and convert to lowercase
        base_name = re.sub(r'[^\w]', '', base_name).lower()

        # Ensure we have a valid filename
        if not base_name or len(base_name) < 1:
            base_name = "audio"

        return base_name

    def _get_unique_filename(self, base_name: str) -> str:
        """Get a unique filename by adding numbers if necessary."""
        filename = f"{base_name}.flac"
        filepath = self.output_dir / filename

        if not filepath.exists():
            return filename

        # File exists, add number
        counter = 1
        while True:
            filename = f"{base_name}{counter}.flac"
            filepath = self.output_dir / filename
            if not filepath.exists():
                return filename
            counter += 1

    def _check_ffmpeg(self) -> bool:
        """Check if ffmpeg is available."""
        try:
            subprocess.run(['ffmpeg', '-version'],
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False

    def download_audio(self) -> str:
        """Download audio from YouTube and convert to FLAC."""
        try:
            # Check if ffmpeg is available
            if not self._check_ffmpeg():
                raise RuntimeError("ffmpeg is required but not found. Please install ffmpeg.")

            print(f"Getting video information from: {self.url}")

            # Get video info
            video_info = self._get_video_info()
            title = video_info.get('title', 'Unknown Title')
            duration = video_info.get('duration', 0)
            uploader = video_info.get('uploader', 'Unknown')

            print(f"Title: {title}")
            print(f"Uploader: {uploader}")
            if duration:
                print(f"Duration: {duration // 60}:{duration % 60:02d}")

            # Generate filename
            base_name = self._generate_filename(title)
            filename = self._get_unique_filename(base_name)
            output_path = self.output_dir / filename

            print(f"Output filename: {filename}")
            print("Downloading audio...")

            # yt-dlp options for audio download
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': str(self.output_dir / '%(title)s.%(ext)s'),
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'flac',
                    'preferredquality': '0',  # Best quality
                }],
                'postprocessor_args': [
                    '-ar', '44100',  # Sample rate
                    '-ac', '2',      # Stereo
                ],
                'prefer_ffmpeg': True,
                'keepvideo': False,
            }

            # Download with yt-dlp
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([self.url])

            # Find the downloaded file (yt-dlp creates it with the full title)
            downloaded_files = list(self.output_dir.glob("*.flac"))
            if not downloaded_files:
                raise RuntimeError("No FLAC file found after download")

            # Get the most recently created file
            latest_file = max(downloaded_files, key=os.path.getctime)

            # Rename to our desired filename if different
            if latest_file.name != filename:
                latest_file.rename(output_path)
                print(f"Renamed to: {filename}")

            # Verify file exists and has content
            if not output_path.exists():
                raise RuntimeError("Download completed but output file not found")

            file_size = output_path.stat().st_size
            if file_size == 0:
                raise RuntimeError("Downloaded file is empty")

            print(f"✓ Download completed successfully!")
            print(f"  File: {output_path}")
            print(f"  Size: {file_size / (1024*1024):.1f} MB")

            return str(output_path)

        except Exception as e:
            print(f"Error downloading audio: {e}")
            sys.exit(1)


def main():
    """Main function to parse arguments and run the downloader."""
    parser = argparse.ArgumentParser(
        description="Download audio from YouTube videos as FLAC files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python ytdl.py "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
  python ytdl.py "https://youtu.be/dQw4w9WgXcQ"

The output filename will be generated from the first two words of the video title.
If a file with the same name exists, a number will be appended.

Requirements:
  - yt-dlp: pip install yt-dlp
  - ffmpeg: Download from https://ffmpeg.org/
        """
    )

    parser.add_argument('url',
                       help='YouTube video URL')
    parser.add_argument('-o', '--output-dir',
                       default='.',
                       help='Output directory (default: current directory)')
    parser.add_argument('--version', action='version', version='YouTube Audio Downloader 1.0')

    args = parser.parse_args()

    # Create and run downloader
    try:
        downloader = YouTubeAudioDownloader(args.url, args.output_dir)
        output_file = downloader.download_audio()

        print(f"\n🎵 Audio successfully downloaded: {output_file}")
        print("You can now use this file with the spectrogram analyzer:")
        print(f"python analyzer.py \"{output_file}\" spectrogram.png png")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()