#!/usr/bin/env python3
"""
Spectrogram Analyzer

A Python script to generate spectrograms from audio files for visualizing
tones and patterns vs. frequency over time.

Usage:
    python analyzer.py <input_file> <output_file> <output_type>

Output types:
    - png: Standard PNG image
    - svg: Scalable Vector Graphics
    - pdf: PDF format
    - interactive: Interactive HTML plot
    - data: Raw spectrogram data (CSV)
"""

import argparse
import sys
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
import csv

try:
    import librosa
    import librosa.display
except ImportError:
    print("Error: librosa is required. Install with: pip install librosa")
    sys.exit(1)

try:
    import plotly.graph_objects as go
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False


class SpectrogramAnalyzer:
    """Main class for generating spectrograms from audio files."""

    def __init__(self, input_file: str, output_file: str, output_type: str):
        self.input_file = Path(input_file)
        self.output_file = Path(output_file)
        self.output_type = output_type.lower()

        # Validate input file
        if not self.input_file.exists():
            raise FileNotFoundError(f"Input file not found: {input_file}")

        # Validate output type
        valid_types = ['png', 'svg', 'pdf', 'interactive', 'data']
        if self.output_type not in valid_types:
            raise ValueError(f"Invalid output type. Must be one of: {valid_types}")

        if self.output_type == 'interactive' and not PLOTLY_AVAILABLE:
            raise ImportError("Plotly is required for interactive output. Install with: pip install plotly")

    def load_audio(self) -> tuple[np.ndarray, int]:
        """Load audio file and return audio data and sample rate."""
        try:
            print(f"Loading audio file: {self.input_file}")
            audio_data, sample_rate = librosa.load(str(self.input_file), sr=None)
            print(f"Audio loaded: {len(audio_data)} samples at {sample_rate} Hz")
            return audio_data, sample_rate
        except Exception as e:
            raise RuntimeError(f"Failed to load audio file: {e}")

    def compute_spectrogram(self, audio_data: np.ndarray, sample_rate: int) -> tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Compute the spectrogram using Short-Time Fourier Transform (STFT)."""
        print("Computing spectrogram...")

        # Compute STFT
        stft = librosa.stft(audio_data, hop_length=512, n_fft=2048)

        # Convert to magnitude spectrogram in dB
        magnitude_db = librosa.amplitude_to_db(np.abs(stft), ref=np.max)

        # Get time and frequency axes
        times = librosa.frames_to_time(np.arange(magnitude_db.shape[1]),
                                     sr=sample_rate, hop_length=512)
        frequencies = librosa.fft_frequencies(sr=sample_rate, n_fft=2048)

        print(f"Spectrogram computed: {magnitude_db.shape[0]} freq bins x {magnitude_db.shape[1]} time frames")
        return magnitude_db, times, frequencies

    def create_matplotlib_plot(self, magnitude_db: np.ndarray, times: np.ndarray,
                             frequencies: np.ndarray, sample_rate: int) -> plt.Figure:
        """Create a matplotlib figure of the spectrogram."""
        fig, ax = plt.subplots(figsize=(12, 8))

        # Create spectrogram plot
        img = librosa.display.specshow(magnitude_db,
                                     x_axis='time',
                                     y_axis='hz',
                                     sr=sample_rate,
                                     hop_length=512,
                                     ax=ax,
                                     cmap='viridis')

        # Add colorbar
        fig.colorbar(img, ax=ax, format='%+2.0f dB')

        # Set labels and title
        ax.set_title(f'Spectrogram: {self.input_file.name}', fontsize=16)
        ax.set_xlabel('Time (s)', fontsize=12)
        ax.set_ylabel('Frequency (Hz)', fontsize=12)

        # Improve layout
        plt.tight_layout()

        return fig

    def create_interactive_plot(self, magnitude_db: np.ndarray, times: np.ndarray,
                              frequencies: np.ndarray) -> str:
        """Create an interactive plotly spectrogram."""
        if not PLOTLY_AVAILABLE:
            raise ImportError("Plotly is required for interactive plots")

        # Create heatmap
        fig = go.Figure(data=go.Heatmap(
            z=magnitude_db,
            x=times,
            y=frequencies,
            colorscale='Viridis',
            colorbar=dict(title="Magnitude (dB)"),
            hovertemplate='Time: %{x:.2f}s<br>Frequency: %{y:.1f}Hz<br>Magnitude: %{z:.1f}dB<extra></extra>'
        ))

        # Update layout
        fig.update_layout(
            title=f'Interactive Spectrogram: {self.input_file.name}',
            xaxis_title='Time (s)',
            yaxis_title='Frequency (Hz)',
            width=1000,
            height=600
        )

        # Generate HTML
        html_content = pyo.plot(fig, output_type='div', include_plotlyjs=True)

        # Wrap in complete HTML document
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Spectrogram: {self.input_file.name}</title>
            <meta charset="utf-8">
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """

        return full_html

    def save_data(self, magnitude_db: np.ndarray, times: np.ndarray,
                  frequencies: np.ndarray) -> None:
        """Save raw spectrogram data as CSV."""
        output_path = self.output_file.with_suffix('.csv')

        print(f"Saving spectrogram data to: {output_path}")

        with open(output_path, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)

            # Write header
            header = ['frequency_hz'] + [f'time_{t:.3f}s' for t in times]
            writer.writerow(header)

            # Write data
            for i, freq in enumerate(frequencies):
                row = [freq] + magnitude_db[i, :].tolist()
                writer.writerow(row)

        # Also save metadata
        metadata = {
            'input_file': str(self.input_file),
            'num_frequencies': len(frequencies),
            'num_time_frames': len(times),
            'frequency_range_hz': [float(frequencies[0]), float(frequencies[-1])],
            'time_range_s': [float(times[0]), float(times[-1])],
            'magnitude_range_db': [float(np.min(magnitude_db)), float(np.max(magnitude_db))]
        }

        metadata_path = self.output_file.with_suffix('.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)

        print(f"Metadata saved to: {metadata_path}")

    def generate_spectrogram(self) -> None:
        """Main method to generate the spectrogram."""
        try:
            # Load audio
            audio_data, sample_rate = self.load_audio()

            # Compute spectrogram
            magnitude_db, times, frequencies = self.compute_spectrogram(audio_data, sample_rate)

            # Generate output based on type
            if self.output_type in ['png', 'svg', 'pdf']:
                fig = self.create_matplotlib_plot(magnitude_db, times, frequencies, sample_rate)

                # Ensure output directory exists
                self.output_file.parent.mkdir(parents=True, exist_ok=True)

                # Save with appropriate extension
                if self.output_type == 'png':
                    output_path = self.output_file.with_suffix('.png')
                    fig.savefig(output_path, dpi=300, bbox_inches='tight')
                elif self.output_type == 'svg':
                    output_path = self.output_file.with_suffix('.svg')
                    fig.savefig(output_path, format='svg', bbox_inches='tight')
                elif self.output_type == 'pdf':
                    output_path = self.output_file.with_suffix('.pdf')
                    fig.savefig(output_path, format='pdf', bbox_inches='tight')

                plt.close(fig)
                print(f"Spectrogram saved to: {output_path}")

            elif self.output_type == 'interactive':
                html_content = self.create_interactive_plot(magnitude_db, times, frequencies)
                output_path = self.output_file.with_suffix('.html')

                # Ensure output directory exists
                self.output_file.parent.mkdir(parents=True, exist_ok=True)

                with open(output_path, 'w') as f:
                    f.write(html_content)

                print(f"Interactive spectrogram saved to: {output_path}")

            elif self.output_type == 'data':
                # Ensure output directory exists
                self.output_file.parent.mkdir(parents=True, exist_ok=True)
                self.save_data(magnitude_db, times, frequencies)

            print("Spectrogram generation completed successfully!")

        except Exception as e:
            print(f"Error generating spectrogram: {e}")
            sys.exit(1)


def main():
    """Main function to parse arguments and run the analyzer."""
    parser = argparse.ArgumentParser(
        description="Generate spectrograms from audio files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Output types:
  png         Standard PNG image (default)
  svg         Scalable Vector Graphics
  pdf         PDF format
  interactive Interactive HTML plot (requires plotly)
  data        Raw spectrogram data as CSV + metadata JSON

Examples:
  python analyzer.py audio.wav spectrogram.png png
  python analyzer.py music.mp3 output/result.html interactive
  python analyzer.py sound.flac data/analysis.csv data
        """
    )

    parser.add_argument('input_file',
                       help='Input audio file path')
    parser.add_argument('output_file',
                       help='Output file path (extension will be adjusted based on output type)')
    parser.add_argument('output_type',
                       choices=['png', 'svg', 'pdf', 'interactive', 'data'],
                       help='Output format type')

    # Optional arguments
    parser.add_argument('--version', action='version', version='Spectrogram Analyzer 1.0')

    args = parser.parse_args()

    # Create and run analyzer
    try:
        analyzer = SpectrogramAnalyzer(args.input_file, args.output_file, args.output_type)
        analyzer.generate_spectrogram()
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()  