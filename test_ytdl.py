#!/usr/bin/env python3
"""
Test script for the YouTube audio downloader filename generation.
This tests the filename logic without requiring yt-dlp.
"""

import sys
import os
import re
from pathlib import Path

# Add the current directory to path to import ytdl
sys.path.insert(0, '.')

def test_filename_generation():
    """Test the filename generation logic."""
    
    # Mock the YouTubeAudioDownloader class methods we need
    class MockDownloader:
        def __init__(self):
            self.output_dir = Path(".")
        
        def _sanitize_filename(self, filename: str) -> str:
            """Sanitize filename by removing invalid characters."""
            # Remove invalid characters for filenames
            sanitized = re.sub(r'[<>:"/\\|?*]', '', filename)
            # Replace multiple spaces with single space
            sanitized = re.sub(r'\s+', ' ', sanitized)
            # Remove leading/trailing spaces
            sanitized = sanitized.strip()
            return sanitized
        
        def _generate_filename(self, title: str) -> str:
            """Generate filename from the first two words of the title."""
            # Clean and split title
            clean_title = self._sanitize_filename(title)
            words = clean_title.split()
            
            if len(words) == 0:
                base_name = "unknown"
            elif len(words) == 1:
                base_name = words[0]
            else:
                # Take first two words and join without space
                base_name = words[0] + words[1]
            
            # Remove any remaining special characters and convert to lowercase
            base_name = re.sub(r'[^\w]', '', base_name).lower()
            
            # Ensure we have a valid filename
            if not base_name or len(base_name) < 1:
                base_name = "audio"
            
            return base_name
        
        def _get_unique_filename(self, base_name: str) -> str:
            """Get a unique filename by adding numbers if necessary."""
            filename = f"{base_name}.flac"
            filepath = self.output_dir / filename
            
            if not filepath.exists():
                return filename
            
            # File exists, add number
            counter = 1
            while True:
                filename = f"{base_name}{counter}.flac"
                filepath = self.output_dir / filename
                if not filepath.exists():
                    return filename
                counter += 1
    
    # Test cases
    test_cases = [
        ("Never Gonna Give You Up", "nevergonna"),
        ("Bohemian Rhapsody - Queen", "bohemianrhapsody"),
        ("Hello World", "helloworld"),
        ("Single", "single"),
        ("", "unknown"),
        ("Special!@#$%^&*()Characters", "specialcharacters"),
        ("Multiple   Spaces   Between", "multiplespaces"),
        ("123 Numbers First", "123numbers"),
        ("Ñoñó Spëcîál Cháracters", "oospecial"),
        ("Very Long Title With Many Words Here", "verylong"),
    ]
    
    downloader = MockDownloader()
    
    print("Testing YouTube title to filename conversion:")
    print("=" * 60)
    
    for title, expected in test_cases:
        result = downloader._generate_filename(title)
        status = "✓" if result == expected else "✗"
        print(f"{status} '{title}' -> '{result}.flac'")
        if result != expected:
            print(f"   Expected: '{expected}.flac'")
    
    print("\nTesting unique filename generation:")
    print("=" * 40)
    
    # Test unique filename generation
    base_name = "test"
    
    # Create some test files to simulate conflicts
    test_files = ["test.flac", "test1.flac", "test2.flac"]
    for test_file in test_files:
        Path(test_file).touch()
    
    try:
        unique_name = downloader._get_unique_filename(base_name)
        print(f"✓ Unique filename for '{base_name}': {unique_name}")
        
        # Should be test3.flac since test.flac, test1.flac, test2.flac exist
        expected_unique = "test3.flac"
        if unique_name == expected_unique:
            print(f"✓ Correctly generated: {unique_name}")
        else:
            print(f"✗ Expected: {expected_unique}, got: {unique_name}")
    
    finally:
        # Clean up test files
        for test_file in test_files:
            try:
                Path(test_file).unlink()
            except FileNotFoundError:
                pass

def test_url_validation():
    """Test URL validation logic."""
    
    # Mock the URL validation method
    def _is_valid_youtube_url(url: str) -> bool:
        """Validate if the URL is a valid YouTube URL."""
        from urllib.parse import urlparse
        
        youtube_domains = [
            'youtube.com', 'www.youtube.com', 'm.youtube.com',
            'youtu.be', 'www.youtu.be'
        ]
        
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            
            # Check for youtube.com URLs
            if any(domain == yt_domain for yt_domain in youtube_domains if 'youtube.com' in yt_domain):
                return 'watch' in parsed.path or 'v=' in parsed.query
            
            # Check for youtu.be URLs
            if any(domain == yt_domain for yt_domain in youtube_domains if 'youtu.be' in yt_domain):
                return len(parsed.path) > 1
            
            return False
        except Exception:
            return False
    
    test_urls = [
        ("https://www.youtube.com/watch?v=dQw4w9WgXcQ", True),
        ("https://youtu.be/dQw4w9WgXcQ", True),
        ("https://m.youtube.com/watch?v=dQw4w9WgXcQ", True),
        ("https://youtube.com/watch?v=dQw4w9WgXcQ", True),
        ("https://www.google.com", False),
        ("https://vimeo.com/123456", False),
        ("not a url", False),
        ("https://youtube.com/", False),
        ("https://youtu.be/", False),
    ]
    
    print("\nTesting URL validation:")
    print("=" * 30)
    
    for url, expected in test_urls:
        result = _is_valid_youtube_url(url)
        status = "✓" if result == expected else "✗"
        print(f"{status} {url} -> {result}")

if __name__ == "__main__":
    print("YouTube Audio Downloader Test Suite")
    print("=" * 50)
    
    test_filename_generation()
    test_url_validation()
    
    print("\nTest completed!")
    print("\nTo test the full downloader, install dependencies:")
    print("pip install yt-dlp")
    print("Then run: python ytdl.py 'https://www.youtube.com/watch?v=example'")
