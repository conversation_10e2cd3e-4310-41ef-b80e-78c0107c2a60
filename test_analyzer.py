#!/usr/bin/env python3
"""
Test script for the spectrogram analyzer.
This script creates a synthetic audio signal and tests the analyzer.
"""

import numpy as np
import wave
import struct
import os
import subprocess
import sys

def create_test_audio(filename="test_audio.wav", duration=2.0, sample_rate=44100):
    """Create a test audio file with multiple frequency components."""
    
    # Time array
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # Create a signal with multiple frequency components
    # 440 Hz (A4) + 880 Hz (A5) + some noise
    signal = (
        0.5 * np.sin(2 * np.pi * 440 * t) +  # A4
        0.3 * np.sin(2 * np.pi * 880 * t) +  # A5
        0.2 * np.sin(2 * np.pi * 1320 * t) + # E6
        0.1 * np.random.normal(0, 0.1, len(t))  # Some noise
    )
    
    # Normalize to prevent clipping
    signal = signal / np.max(np.abs(signal)) * 0.8
    
    # Convert to 16-bit integers
    signal_int = (signal * 32767).astype(np.int16)
    
    # Write WAV file
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 2 bytes per sample
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(signal_int.tobytes())
    
    print(f"Created test audio file: {filename}")
    return filename

def test_analyzer():
    """Test the analyzer with different output types."""
    
    # Create test audio
    test_file = create_test_audio()
    
    # Test cases
    test_cases = [
        ("png", "test_output.png"),
        ("svg", "test_output.svg"),
        ("pdf", "test_output.pdf"),
        ("data", "test_output.csv"),
    ]
    
    # Only test interactive if plotly is available
    try:
        import plotly
        test_cases.append(("interactive", "test_output.html"))
    except ImportError:
        print("Plotly not available, skipping interactive test")
    
    print("\nTesting analyzer with different output types:")
    print("=" * 50)
    
    for output_type, output_file in test_cases:
        print(f"\nTesting {output_type} output...")
        
        try:
            # Run the analyzer
            cmd = [sys.executable, "analyzer.py", test_file, output_file, output_type]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✓ {output_type} test passed")
                print(f"  Output: {result.stdout.strip()}")
                
                # Check if output file was created
                expected_extensions = {
                    "png": ".png",
                    "svg": ".svg", 
                    "pdf": ".pdf",
                    "data": ".csv",
                    "interactive": ".html"
                }
                
                expected_file = output_file.replace(
                    os.path.splitext(output_file)[1], 
                    expected_extensions[output_type]
                )
                
                if os.path.exists(expected_file):
                    file_size = os.path.getsize(expected_file)
                    print(f"  Created: {expected_file} ({file_size} bytes)")
                else:
                    print(f"  Warning: Expected output file not found: {expected_file}")
                    
            else:
                print(f"✗ {output_type} test failed")
                print(f"  Error: {result.stderr.strip()}")
                
        except subprocess.TimeoutExpired:
            print(f"✗ {output_type} test timed out")
        except Exception as e:
            print(f"✗ {output_type} test error: {e}")
    
    # Cleanup
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"\nCleaned up test file: {test_file}")

if __name__ == "__main__":
    print("Spectrogram Analyzer Test Suite")
    print("=" * 40)
    
    # Check if dependencies are available
    missing_deps = []
    
    try:
        import matplotlib
    except ImportError:
        missing_deps.append("matplotlib")
    
    try:
        import librosa
    except ImportError:
        missing_deps.append("librosa")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    if missing_deps:
        print(f"Missing dependencies: {', '.join(missing_deps)}")
        print("Please install with: pip install -r requirements.txt")
        sys.exit(1)
    
    test_analyzer()
    print("\nTest completed!")
